<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  20 June 2025
 *
 * Description: Only Report section Latest Boostrap.
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Reports Fees_structure
*/
class Reports_v3 extends CI_Controller {

     private $yearId;
	public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('FEESV2')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/reports_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->helper('texting_helper');
    $this->load->helper('fees_helper');
    $this->load->model('avatar');
    $this->load->library('filemanager');
    $this->load->model('communication/emails_model', 'emails');
    $this->yearId = $this->acad_year->getAcadYearId();
  }

  public function day_books1(){
    $data['report_title'] = 'Daily Transaction Report';
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_daily_transactions();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['payment_mode'] = [];
    if(!empty($data['fee_blueprints'])){
      $data['payment_mode'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    }
    $data['additionalAmount'] = $this->reports_model->get_payment_options_additional_amount();
    $data['sales'] = $this->authorization->isModuleEnabled('SALES');
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/daily_transaction_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function student_wise_fees_details($fee_type ='', $clsId =''){
    $data['report_title'] = 'Fee Detail Report';
    $data['fee_type'] = $fee_type;
    $data['clsId'] = $clsId;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['combination'] = $this->Student_Model->getCombList();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['category'] = $this->settings->getSetting('category');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['refundAmount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['main_content'] = 'feesv2/reports/student_fees_summary_v2';
    $this->load->view('inc/template_reportV3', $data);
    // $this->load->view('feesv2/reports/inc/reports_header', $data);
  }

    public function balance_report($fee_type=''){
    $data['report_title'] = 'Fees Balance / SMS Report';
    $data['fee_type'] = $fee_type;
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['email_template'] = $this->reports_model->get_email_template_for_fee();
    $data['sms_template'] = $this->reports_model->get_sms_template_for_fee();
    $data['email_option'] = $this->settings->getSetting('fee_balance_from_email');
    $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['combinationList'] = $this->Student_Model->getCombinations();
    $data['student_names'] = $this->reports_model->get_fees_balance_student_names();
    $data['adSelected'] = ['1','2']; // 2 is default select admission status approved
    if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'feesv2/reports/balance_sms_mobile_v2';
    }else{
      $data['main_content'] = 'feesv2/reports/balance_sms_v2';     
    }
    $this->load->view('inc/template_reportV3', $data);
  }

  
  public function reconciled_report_v2(){
    $data['report_title'] = 'Reconciled Report';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    $reconcilation = $this->input->post('reconcilation');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $data['selected_blueprint'] = $fee_type_id;
    $data['reconcilation'] = $reconcilation;
    $data['from_date'] = $from_date;
    $data['to_date'] = $to_date;
    $data['reconciled'] = $this->reports_model->get_reconciled_details($fee_type_id, $reconcilation, $from_date, $to_date);
    // echo "<pre>"; print_r($data['reconciled']); die();
    // $data['main_content'] = 'feesv2/reports/non_reconciled';
    $data['main_content'] = 'feesv2/student/non_reconciled_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

    public function daily_transcation_prarthana_new_v2(){
    $data['report_title'] = 'Daily Transaction Fee Type';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_all_branches();
    $data['main_content'] = 'feesv2/reports/daily_transcation_prarthana_new_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function fine_waiver_report(){
    $data['report_title'] = 'Fine Waiver report';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/fine_waiver_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

    public function concessions_day_report_v2($fee_type ='') {
      $data['report_title'] = 'Concession Day Report';
    $data['fee_type'] = $fee_type;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/view_concession_day_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }
}